/* Font Imports */
@import url("https://fonts.googleapis.com/css2?family=Merriweather:wght@400;700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Josefin+Sans:ital,wght@0,400;1,400&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@400;600&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Battambang:wght@400;700&display=swap");

/* ThankYou Page Styles */
.thankyou {
  min-height: 100vh;
  background-color: #fdfbf4;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  font-family: 'Source Sans Pro', sans-serif;
}

.thankyou__container {
  max-width: 1400px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40px;
}

/* Main message at top center */
.thankyou__main-message {
  text-align: center;
  width: 100%;
  margin-bottom: 20px;
}

/* Two-column layout */
.thankyou__content {
  display: flex;
  justify-content: center;
  grid-template-columns: 1fr 450px;
  gap: 60px;
  width: 100%;
  
}

.thankyou__left-column {
  display: flex;
  flex-direction: column;
  
  gap: 40px;
}

.thankyou__right-column {
  display: flex;
  flex-direction: column;
  top: 40px;
}

/* Main Thank You Message - Top Center */
.thankyou__title {
  font-family: 'Baskerville Old Face', 'Times New Roman', 'Georgia', serif;
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 400;
  color: #966f33;
  margin: 0 0 15px 0;
  line-height: 1.2;
  letter-spacing: -0.02em;
}

.thankyou__subtitle {
  font-family: 'Josefin Sans', sans-serif;
  font-style: italic;
  font-size: clamp(1.2rem, 2.5vw, 1.5rem);
  color: #966f33;
  margin: 0 0 40px 0;
  opacity: 0.9;
}

/* Social Media Post Container */
.thankyou__social-post {
  background-color: #ffffff;
  border: 2px solid #e5cca4;
  border-radius: 0; /* Sharp rectangular corners as per user preference */
  padding: 30px;
  width: 100%;
  max-width: 600px;
  height: 550px;
  box-shadow: 0 8px 32px rgba(150, 111, 51, 0.1);
}

.thankyou__post-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e5cca4;
}

.thankyou__post-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%; /* Only profile pictures get rounded corners */
  overflow: hidden;
  border: 2px solid #966f33;
}

.thankyou__avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thankyou__post-info {
  flex: 1;
}

.thankyou__post-username {
  font-family: 'Battambang', sans-serif;
  font-size: 18px;
  font-weight: 700;
  color: #966f33;
  margin: 0 0 5px 0;
}

.thankyou__post-time {
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 14px;
  color: #966f33;
  opacity: 0.7;
  margin: 0;
}

.thankyou__post-content {
  margin-bottom: 30px;
}

.thankyou__post-heading {
  font-family: 'Battambang', sans-serif;
  font-size: clamp(1.3rem, 2.5vw, 1.6rem);
  font-weight: 700;
  color: #966f33;
  margin: 0 0 20px 0;
  line-height: 1.4;
}

.thankyou__post-description {
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: #333333;
  margin: 0;
}

/* Social Media Icons */
.thankyou__social-icons {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.thankyou__social-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 15px 12px;
  background-color: #fdfbf4;
  border: 2px solid #e5cca4;
  text-decoration: none;
  transition: all 0.3s ease;
  min-width: 80px;
  border-radius: 0; /* Sharp rectangular corners */
}

.thankyou__social-icon:hover {
  border-color: var(--social-color, #966f33);
  background-color: #ffffff;
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(150, 111, 51, 0.2);
}

.thankyou__social-svg {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #966f33;
  transition: color 0.3s ease;
}

.thankyou__social-icon:hover .thankyou__social-svg {
  color: var(--social-color, #966f33);
}

.thankyou__social-name {
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 12px;
  font-weight: 600;
  color: #966f33;
  text-align: center;
}

.thankyou__social-icon:hover .thankyou__social-name {
  color: var(--social-color, #966f33);
}

/* Share button states */
.thankyou__social-icon:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.thankyou__social-icon.shared {
  background-color: #e8f5e8;
  border-color: #4caf50;
}

.thankyou__social-icon.shared .thankyou__social-svg,
.thankyou__social-icon.shared .thankyou__social-name {
  color: #4caf50;
}

.thankyou__social-icon.loading {
  opacity: 0.7;
}

.share-checkmark {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #4caf50;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

/* Back to Home Button */
.thankyou__back-button {
  margin-top: 20px;
}

.thankyou__custom-back-btn {
  display: flex;
  gap: 10px;
  justify-content: center;
  align-items: center;
  padding: 10px 15px;
  border: 2px solid #927D70;
  cursor: pointer;
  font-family: 'Merriweather', serif;
  font-weight: bold;
  letter-spacing: 0.1em;
  font-size: 17px;
  line-height: 24px;
  
  height: 50px;
  background-color: #927D70;
  color: white;
  border-radius: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.thankyou__custom-back-btn:hover {
  background-color: transparent;
  color: #927D70;
  border-color: #927D70;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(146, 125, 112, 0.3);
}

.thankyou__custom-back-btn:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(146, 125, 112, 0.2);
}

.thankyou__custom-back-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
  background-color: #927D70;
  color: white;
  border-color: #927D70;
}

@media (max-width: 640px) {
  .thankyou__custom-back-btn {
    width: 100%;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .thankyou {
    padding: 30px 15px;
  }

  .thankyou__container {
    gap: 40px;
  }

  .thankyou__social-post {
    padding: 25px 20px;
  }

  .thankyou__post-header {
    gap: 12px;
    margin-bottom: 20px;
    padding-bottom: 15px;
  }

  .thankyou__post-avatar {
    width: 40px;
    height: 40px;
  }

  .thankyou__post-username {
    font-size: 16px;
  }

  .thankyou__post-time {
    font-size: 12px;
  }

  .thankyou__post-description {
    font-size: 15px;
    line-height: 1.5;
  }

  .thankyou__social-icons {
    gap: 15px;
  }

  .thankyou__social-icon {
    padding: 12px 10px;
    min-width: 70px;
  }

  .thankyou__social-svg svg {
    width: 20px;
    height: 20px;
  }

  .thankyou__social-name {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .thankyou {
    padding: 20px 10px;
  }

  .thankyou__container {
    gap: 30px;
  }

  .thankyou__social-post {
    padding: 20px 15px;
  }

  .thankyou__social-icons {
    gap: 12px;
  }

  .thankyou__social-icon {
    padding: 10px 8px;
    min-width: 60px;
  }

  .thankyou__social-svg svg {
    width: 18px;
    height: 18px;
  }

  .thankyou__social-name {
    font-size: 10px;
  }
}

/* Professional Leaderboard Styles */
.leaderboard {
  background: white;
  border-radius: 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e0e0e0;
  overflow: hidden;
  height: 550px;
  max-width: 100%;
  display: flex;
  flex-direction: column;
}

.leaderboard__header {
  background: #1a1a1a;
  color: white;
  padding: 25px 30px 20px;
  border-bottom: 3px solid #d4af37;
}

.leaderboard__title {
  font-family: 'Baskerville Old Face', serif;
  font-size: 1.5rem;
  margin: 0 0 5px 0;
  color: #d4af37;
  font-weight: normal;
  text-align: center;
}

.leaderboard__subtitle {
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 0.9rem;
  margin: 0 0 20px 0;
  color: #ccc;
  text-align: center;
}

.leaderboard__tabs {
  display: flex;
  gap: 0;
  margin-top: 15px;
}

.leaderboard__tab {
  flex: 1;
  background: transparent;
  border: 1px solid #444;
  color: #ccc;
  padding: 8px 16px;
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.leaderboard__tab:first-child {
  border-right: none;
}

.leaderboard__tab--active {
  background: #d4af37;
  color: #1a1a1a;
  border-color: #d4af37;
}

.leaderboard__tab:hover:not(.leaderboard__tab--active) {
  background: #333;
  color: white;
}

.leaderboard__content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.leaderboard__list {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.leaderboard__list::-webkit-scrollbar {
  width: 6px;
}

.leaderboard__list::-webkit-scrollbar-track {
  background: #f5f5f5;
}

.leaderboard__list::-webkit-scrollbar-thumb {
  background: #d4af37;
  border-radius: 3px;
}

.leaderboard__list::-webkit-scrollbar-thumb:hover {
  background: #b8941f;
}

.leaderboard__item {
  display: flex;
  align-items: center;
  padding: 18px 30px;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.3s ease;
  position: relative;
}

.leaderboard__item:hover {
  background: linear-gradient(90deg, #fafafa 0%, #f5f5f5 100%);
  border-left: 3px solid #d4af37;
  padding-left: 27px;
}

.leaderboard__item:last-child {
  border-bottom: none;
}

.leaderboard__rank {
  width: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
}

.leaderboard__medal {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.leaderboard__medal--gold {
  color: #d4af37;
}

.leaderboard__medal--silver {
  color: #c0c0c0;
}

.leaderboard__medal--bronze {
  color: #cd7f32;
}

.leaderboard__medal-icon {
  font-size: 1.2rem;
}

.leaderboard__rank-badge {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #e0e0e0;
}

.leaderboard__rank-number {
  font-family: 'Battambang', sans-serif;
  font-weight: 700;
  font-size: 0.9rem;
  color: #666;
}

.leaderboard__medal .leaderboard__rank-number {
  font-size: 0.75rem;
  color: inherit;
}

.leaderboard__info {
  flex: 1;
}

.leaderboard__name {
  font-family: 'Battambang', sans-serif;
  font-size: 0.95rem;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #1a1a1a;
  line-height: 1.3;
}

.leaderboard__stats {
  display: flex;
  gap: 20px;
}

.leaderboard__stat {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.leaderboard__stat-value {
  font-family: 'Battambang', sans-serif;
  font-weight: 700;
  font-size: 0.9rem;
  color: #d4af37;
  line-height: 1;
}

.leaderboard__stat-label {
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 0.75rem;
  color: #888;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 2px;
}

.leaderboard__footer {
  background: linear-gradient(135deg, #f8f8f8 0%, #f0f0f0 100%);
  padding: 20px 30px;
  border-top: 1px solid #e0e0e0;
}

.leaderboard__user-position {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.leaderboard__user-rank {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.leaderboard__user-rank-label {
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 0.75rem;
  color: #888;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 2px;
}

.leaderboard__user-rank-value {
  font-family: 'Battambang', sans-serif;
  font-weight: 700;
  font-size: 1.1rem;
  color: #1a1a1a;
}

.leaderboard__user-points {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  text-align: right;
}

.leaderboard__user-points-value {
  font-family: 'Battambang', sans-serif;
  font-weight: 700;
  font-size: 1rem;
  color: #d4af37;
  margin-bottom: 2px;
}

.leaderboard__user-points-label {
  font-family: 'Josefin Sans', sans-serif;
  font-style: italic;
  font-size: 0.8rem;
  color: #666;
}

/* Back to Home Button - Updated for grid layout */
.thankyou__back-button {
  
  position: absolute;
  left:10px;
  top:0;

  display: flex;
  justify-content: center;
}

/* === Redesigned Layout === */
.thankyou__content-redesign {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: flex-start;
  width: 100%;
  gap: 48px;
  min-height: 700px;
}

.thankyou__main-content {
  flex: 0 1 70%;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 0;
}

.thankyou__sidebar-leaderboard {
  flex: 0 0 370px;
  max-width: 370px;
  min-width: 320px;
  width: 30%;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  position: relative;
  z-index: 2;
}

/* === Glassmorphic Thank You Card === */
.thankyou__card-glass {
  width: 100%;
  max-width: 650px;
  min-width: 340px;
  min-height: 520px;
  background: rgba(253, 251, 244, 0.85);
  border: 2px solid rgba(150, 111, 51, 0.18);
  box-shadow: 0 8px 40px 0 rgba(150, 111, 51, 0.13), 0 1.5px 8px 0 rgba(0,0,0,0.07);
  backdrop-filter: blur(18px) saturate(1.2);
  -webkit-backdrop-filter: blur(18px) saturate(1.2);
  border-radius: 18px;
  padding: 54px 48px 40px 48px;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 1;
  box-sizing: border-box;
}

.thankyou__title-glass {
  font-family: 'Baskerville Old Face', 'Times New Roman', serif;
  font-size: 2.8rem;
  color: #966f33;
  font-weight: 400;
  margin: 0 0 12px 0;
  letter-spacing: -0.02em;
  text-align: center;
}

.thankyou__desc-glass {
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 1.25rem;
  color: #966f33;
  margin: 0 0 28px 0;
  text-align: center;
  opacity: 0.92;
}

.thankyou__divider-glass {
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, #966f33 0%, #fdfbf4 100%);
  opacity: 0.18;
  margin: 0 0 32px 0;
}

.thankyou__celebrate-message {
  width: 100%;
  margin-bottom: 32px;
}

.thankyou__card-heading {
  font-family: 'Baskerville Old Face', 'Times New Roman', serif;
  font-size: 1.45rem;
  color: #966f33;
  font-weight: 600;
  margin: 0 0 16px 0;
  text-align: center;
  letter-spacing: -0.01em;
}

.thankyou__card-description {
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 1.1rem;
  color: #222;
  margin: 0;
  text-align: center;
  line-height: 1.7;
}

.thankyou__social-row {
  display: flex;
  flex-direction: row;
  gap: 22px;
  justify-content: center;
  align-items: center;
  margin: 32px 0 0 0;
  width: 100%;
}

.thankyou__social-icon-glass {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 7px;
  padding: 14px 12px;
  background: rgba(255, 248, 228, 0.85);
  border: 2px solid #e5cca4;
  border-radius: 8px;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 80px;
  box-shadow: 0 2px 12px rgba(150, 111, 51, 0.08);
  cursor: pointer;
  position: relative;
}

.thankyou__social-icon-glass:hover {
  border-color: var(--social-color, #966f33);
  background: rgba(253, 251, 244, 0.98);
  transform: translateY(-2px) scale(1.04);
  box-shadow: 0 6px 24px rgba(150, 111, 51, 0.18);
}

.thankyou__social-svg {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #966f33;
  transition: color 0.3s ease;
}

.thankyou__social-icon-glass:hover .thankyou__social-svg {
  color: var(--social-color, #966f33);
}

.thankyou__social-name {
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 13px;
  font-weight: 600;
  color: #966f33;
  text-align: center;
}

.thankyou__social-icon-glass:hover .thankyou__social-name {
  color: var(--social-color, #966f33);
}

.thankyou__social-icon-glass:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.thankyou__social-icon-glass.shared {
  background: #e8f5e8;
  border-color: #4caf50;
}

.thankyou__social-icon-glass.shared .thankyou__social-svg,
.thankyou__social-icon-glass.shared .thankyou__social-name {
  color: #4caf50;
}

.thankyou__social-icon-glass.loading {
  opacity: 0.7;
}

.share-checkmark {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #4caf50;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.thankyou__back-button-glass {
  margin-top: 38px;
  width: 100%;
  display: flex;
  justify-content: center;
}

/* === Responsive Design === */
@media (max-width: 1200px) {
  .thankyou__content-redesign {
    gap: 24px;
  }
  .thankyou__sidebar-leaderboard {
    min-width: 260px;
    max-width: 300px;
  }
  .thankyou__card-glass {
    padding: 36px 18px 28px 18px;
    max-width: 98vw;
  }
}

@media (max-width: 900px) {
  .thankyou__content-redesign {
    flex-direction: column;
    align-items: center;
    gap: 32px;
  }
  .thankyou__main-content, .thankyou__sidebar-leaderboard {
    width: 100%;
    max-width: 100%;
    min-width: 0;
  }
  .thankyou__sidebar-leaderboard {
    margin-top: 0;
    min-width: 0;
    max-width: 100vw;
  }
  .thankyou__card-glass {
    min-width: 0;
    width: 100%;
    max-width: 98vw;
  }
}

@media (max-width: 600px) {
  .thankyou__card-glass {
    padding: 18px 4vw 18px 4vw;
    min-height: 340px;
  }
  .thankyou__title-glass {
    font-size: 1.5rem;
  }
  .thankyou__desc-glass {
    font-size: 1rem;
  }
  .thankyou__card-heading {
    font-size: 1.1rem;
  }
  .thankyou__social-row {
    gap: 10px;
    margin-top: 18px;
  }
  .thankyou__sidebar-leaderboard {
    min-width: 0;
    max-width: 100vw;
  }
}

/* === End Redesign === */

/* Animation classes for GSAP */
.thankyou__main-message,
.thankyou__social-post,
.thankyou__social-icon,
.thankyou__back-button,
.thankyou__left-column,
.thankyou__right-column,
.leaderboard__item {
  will-change: transform, opacity;
}

/* Mobile Responsive Design - 767px and below */
@media (max-width: 767px) {
  .thankyou--mobile {
    padding: 20px 15px;
    min-height: 100vh;
  }

  .thankyou__container {
    gap: 30px;
    max-width: 100%;
  }

  .thankyou__main-message {
    margin-bottom: 15px;
  }

  .thankyou__title {
    font-family: "Baskerville Old Face", "Times New Roman", serif;
    font-size: clamp(28px, 8vw, 36px) !important;
    color: #966f33 !important;
    margin-bottom: 15px !important;
    letter-spacing: -0.02em;
    line-height: 1.1;
  }

  .thankyou__subtitle {
    font-family: "Source Sans Pro", sans-serif;
    font-size: clamp(16px, 4vw, 18px) !important;
    color: #3e3e3e !important;
    margin-bottom: 0 !important;
    letter-spacing: -0.01em;
  }

  /* Mobile Content Layout - Single Column with Reordering */
  .thankyou__content--mobile {
    flex-direction: column !important;
    gap: 30px !important;
    align-items: center;
  }

  .thankyou__left-column {
    width: 100% !important;
    max-width: 100%;
    gap: 25px !important;
    order: 1; /* Social post appears first */
    padding: 0 15px;
  }

  .thankyou__right-column {
    width: 100% !important;
    max-width: 100%;
    order: 2; /* Leaderboard appears second */
    padding: 0 15px;
  }

  /* Mobile Social Post Styling */
  .thankyou__social-post {
    padding: 25px 20px !important;
    border-radius: 12px !important;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1) !important;
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
  }

  .thankyou__post-header {
    margin-bottom: 20px !important;
  }

  .thankyou__post-avatar {
    width: 50px !important;
    height: 50px !important;
  }

  .thankyou__avatar-image {
    width: 50px !important;
    height: 50px !important;
  }

  .thankyou__post-username {
    font-family: "Battambang", sans-serif !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    color: #966f33 !important;
  }

  .thankyou__post-time {
    font-family: "Source Sans Pro", sans-serif !important;
    font-size: 12px !important;
    color: #666 !important;
  }

  .thankyou__post-heading {
    font-family: "Baskerville Old Face", "Times New Roman", serif !important;
    font-size: clamp(18px, 5vw, 22px) !important;
    color: #3e3e3e !important;
    margin-bottom: 15px !important;
    line-height: 1.3 !important;
    letter-spacing: -0.02em;
  }

  .thankyou__post-description {
    font-family: "Source Sans Pro", sans-serif !important;
    font-size: 14px !important;
    color: #3e3e3e !important;
    line-height: 1.5 !important;
    margin-bottom: 20px !important;
  }

  /* Mobile Social Icons - Horizontal Layout */
  .thankyou__social-icons {
    display: flex !important;
    flex-direction: row !important;
    gap: 15px !important;
    justify-content: center !important;
    flex-wrap: wrap !important;
    margin-top: 20px !important;
  }

  .thankyou__social-icon {
    width: 48px !important;
    height: 48px !important;
    border-radius: 8px !important;
    min-width: 48px !important;
    flex-shrink: 0 !important;
  }

  .thankyou__social-icon svg {
    width: 22px !important;
    height: 22px !important;
  }

  /* Mobile Leaderboard - Full Width */
  .leaderboard {
    width: 100% !important;
    max-width: 100% !important;
    padding: 20px 0 !important;
    border-radius: 12px !important;
    max-height: 500px !important;
    margin: 0 !important;
  }

  .leaderboard__header {
    padding: 20px 20px 15px 20px !important;
  }

  .leaderboard__content {
    padding: 0 20px 20px 20px !important;
  }

  .leaderboard__title {
    font-family: "Baskerville Old Face", "Times New Roman", serif !important;
    font-size: clamp(20px, 5vw, 24px) !important;
    color: #966f33 !important;
    margin-bottom: 8px !important;
    text-align: center !important;
  }

  .leaderboard__subtitle {
    font-family: "Source Sans Pro", sans-serif !important;
    font-size: 14px !important;
    color: #666 !important;
    text-align: center !important;
    margin-bottom: 15px !important;
  }

  .leaderboard__item {
    padding: 15px 0 !important;
    margin-bottom: 0 !important;
    border-radius: 0 !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    width: 100% !important;
  }

  .leaderboard__item:last-child {
    border-bottom: none !important;
  }

  .leaderboard__rank {
    font-family: "Battambang", sans-serif !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    color: #966f33 !important;
  }

  .leaderboard__name {
    font-family: "Source Sans Pro", sans-serif !important;
    font-size: 15px !important;
    color: #3e3e3e !important;
    font-weight: 600 !important;
  }

  .leaderboard__stat-value {
    font-family: "Source Sans Pro", sans-serif !important;
    font-size: 13px !important;
    color: #666 !important;
    font-weight: 600 !important;
  }

  .leaderboard__stat-label {
    font-family: "Source Sans Pro", sans-serif !important;
    font-size: 11px !important;
    color: #999 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
  }

  /* Mobile Back Button */
  .thankyou__back-button {
    margin-top: 20px !important;
    width: 100% !important;
    max-width: 280px !important;
    padding: 12px 24px !important;
    font-family: "Merriweather", serif !important;
    font-size: 16px !important;
  }

  /* Mobile Loading States */
  .thankyou__social-icon--loading {
    opacity: 0.6 !important;
  }

  .thankyou__social-icon:active {
    transform: scale(0.95) !important;
  }

  .thankyou__back-button:active {
    transform: scale(0.98) !important;
  }

  /* Mobile Leaderboard Tabs */
  .leaderboard__tabs {
    gap: 10px !important;
    margin-bottom: 15px !important;
  }

  .leaderboard__tab {
    padding: 8px 16px !important;
    font-size: 13px !important;
    border-radius: 6px !important;
  }

  /* Mobile Medal Styling */
  .leaderboard__medal {
    width: 35px !important;
    height: 35px !important;
  }

  .leaderboard__medal-icon {
    font-size: 16px !important;
  }

  .leaderboard__rank-number {
    font-size: 12px !important;
  }

  /* Mobile Stats Layout */
  .leaderboard__stats {
    gap: 15px !important;
  }

  .leaderboard__stat {
    min-width: auto !important;
  }

  /* Ensure full width on very small screens */
  @media (max-width: 480px) {
    .thankyou__left-column,
    .thankyou__right-column {
      padding: 0 10px !important;
    }

    .thankyou__social-post {
      padding: 20px 15px !important;
    }

    .leaderboard__header,
    .leaderboard__content {
      padding-left: 15px !important;
      padding-right: 15px !important;
    }
  }
}
